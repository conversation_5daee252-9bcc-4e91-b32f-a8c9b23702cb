# train_model.py
import joblib
from sklearn.naive_bayes import MultinomialNB
from sklearn.feature_extraction.text import CountVectorizer

# ===== INTENTS & TRAINING PHRASES =====
TRAIN_DEFAULTS = {
    "full_name": {
        "x": [
            "what is your full name",
            "tell me your name",
            "may I know your full name",
            "who are you",
            "what should I call you"
        ]
    },
    "origin": {
        "x": [
            "where are you from",
            "which country are you from",
            "your birthplace",
            "tell me your origin",
            "where were you born"
        ]
    },
    "current_location": {
        "x": [
            "where do you live",
            "what is your current location",
            "where are you staying",
            "which city do you live in",
            "your home location"
        ]
    },
    "education": {
        "x": [
            "what is your education background",
            "tell me about your education",
            "where did you study",
            "what degree do you have",
            "your qualification"
        ]
    },
    "tutoring_career": {
        "x": [
            "tell me about your tutoring career",
            "how long have you been teaching",
            "what do you teach",
            "describe your teaching experience",
            "your tutoring history"
        ]
    },
    "professional_career": {
        "x": [
            "tell me about your professional career",
            "what do you do for work",
            "where do you work",
            "describe your job experience",
            "what is your profession"
        ]
    },
    "tools_and_skills": {
        "x": [
            "what tools do you know",
            "which programming languages do you know",
            "what software do you use",
            "what is your tech stack",
            "your skills"
        ]
    },
    "childhood": {
        "x": [
            "tell me about your childhood",
            "how was your childhood",
            "what was your early life like",
            "describe your early days",
            "share your childhood memories"
        ]
    },
    "personal_life": {
        "x": [
            "tell me about your personal life",
            "are you married",
            "do you have kids",
            "what are your hobbies",
            "what do you do in your free time"
        ]
    }
}

# ===== PREDEFINED ANSWERS =====
DEFAULT_ANSWERS = {
    "full_name": "My full name is Faruk Hasan.",
    "origin": "I am originally from Bangladesh.",
    "current_location": "I currently live in New York, USA.",
    "education": "I hold a degree in Computer Science and have pursued additional certifications in software testing and AI.",
    "tutoring_career": "I teach coding, AI, and tech courses online, focusing on Python, Java, web development, and AI for kids and teens.",
    "professional_career": "I am a Senior QA Engineer specializing in test automation, AI integration in QA, and web application testing.",
    "tools_and_skills": "I work with Python, Java, JavaScript, Playwright, Selenium, GitHub, AWS, and various AI tools.",
    "childhood": "I grew up in Bangladesh, where I developed a love for technology and problem-solving at a young age.",
    "personal_life": "I’m married and have twin kids. I enjoy teaching, writing, traveling, and working on personal tech projects."
}

# ===== TRAINING FUNCTION =====
def build_training_corpus(intents):
    X, y = [], []
    for label, data in intents.items():
        for phrase in data["x"]:
            X.append(phrase)
            y.append(label)
    return X, y

def train_and_dump(
    model_path="model.pkl",
    vectorizer_path="vectorizer.pkl",
    answers_path="answers.pkl"
):
    X, y = build_training_corpus(TRAIN_DEFAULTS)
    vectorizer = CountVectorizer(ngram_range=(1, 2), lowercase=True, strip_accents="unicode")
    Xv = vectorizer.fit_transform(X)
    model = MultinomialNB()
    model.fit(Xv, y)

    joblib.dump(model, model_path)
    joblib.dump(vectorizer, vectorizer_path)
    joblib.dump(DEFAULT_ANSWERS, answers_path)
    print("Training complete. Artifacts saved.")

if __name__ == "__main__":
    train_and_dump()
