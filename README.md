---
title: <PERSON><PERSON> – Personal Chatbot
emoji: 🤖
colorFrom: indigo
colorTo: purple
sdk: gradio
app_file: app.py
pinned: false
license: mit
tags:
  - chatbot
  - personal
  - portfolio
  - gradio
  - resume
---

# 🤖 <PERSON><PERSON> – Personal Chatbot

Ask about my education, tools/skills, professional experience, tutoring, childhood, and personal life.  
Built with Python, scikit‑learn (Naive <PERSON>es), and Gradio.

## Quick start (local)
```bash
pip install -r requirements.txt
python train_model.py
python app.py
