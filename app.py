# app.py
import joblib
import gradio as gr

MODEL_PATH = "model.pkl"
VECTORIZER_PATH = "vectorizer.pkl"
ANSWERS_PATH = "answers.pkl"

# Load model, vectorizer, and answers
model = joblib.load(MODEL_PATH)
vectorizer = joblib.load(VECTORIZER_PATH)
answers = joblib.load(ANSWERS_PATH)

def chatbot_response(user_input):
    X = vectorizer.transform([user_input])
    intent = model.predict(X)[0]
    return answers.get(intent, "Sorry, I don't have an answer for that.")

# Gradio UI
with gr.Blocks(title="Far<PERSON> Hasan - Personal Chatbot") as demo:
    gr.Markdown("## 🤖 Ask me anything about my personal and professional life!")
    chatbot = gr.Chatbot()
    msg = gr.Textbox(placeholder="Ask me something...", label="Your question")

    def respond(message, chat_history):
        bot_reply = chatbot_response(message)
        chat_history.append((message, bot_reply))
        return "", chat_history

    msg.submit(respond, [msg, chatbot], [msg, chatbot])

if __name__ == "__main__":
    demo.launch()
