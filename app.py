# app.py
"""
Compact Gradio UI for the personal chatbot (PROFILE-based).
- Chat column first (mobile: chat shows before sidebar)
- Sticky input row
- Reduced paddings for tight iframes
"""

import os
import pathlib
import subprocess
from typing import Dict, Any, Callable, List

import joblib
import gradio as gr

MODEL_PATH = os.getenv("MODEL_PATH", "model.pkl")
VECTORIZER_PATH = os.getenv("VECTORIZER_PATH", "vectorizer.pkl")
ANSWERS_PATH = os.getenv("ANSWERS_PATH", "answers.pkl")


def ensure_artifacts():
    need = [MODEL_PATH, VECTORIZER_PATH, ANSWERS_PATH]
    if not all(pathlib.Path(p).exists() for p in need):
        print("[INFO] Artifacts missing — training model...")
        subprocess.run(["python", "train_model.py"], check=True)
        print("[INFO] Training finished.")


ensure_artifacts()

# --- Load artifacts ---
model = joblib.load(MODEL_PATH)
vectorizer = joblib.load(VECTORIZER_PATH)
packed = joblib.load(ANSWERS_PATH)
answers_index = packed["answers_index"]
PROFILE: Dict[str, Any] = packed["profile"]

# --- Renderers (mirror train_model.py keys) ---
def render_full_name(p: Dict[str, Any]) -> str:
    return f"My full name is {p.get('full_name', '—')}."

def render_origin(p: Dict[str, Any]) -> str:
    bp = p.get("birthplace")
    if bp:
        return f"I was born in {bp}."
    origin = p.get("origin")
    return f"I’m originally from {origin}." if origin else "I’m originally from —."

def render_location(p: Dict[str, Any]) -> str:
    loc = p.get("current_location")
    return f"I currently live in {loc}." if loc else "I currently live in —."

def render_education(p: Dict[str, Any]) -> str:
    edu: List[Dict[str, str]] = p.get("education", [])
    if not edu:
        return "Education: (add items in PROFILE['education'])."
    lines = []
    for e in edu:
        inst = e.get("institution", "Institution")
        deg  = e.get("degree", "").strip()
        fld  = e.get("field", "").strip()
        yrs  = e.get("years", "").strip()
        notes = e.get("notes", "").strip()
        parts = [v for v in [deg, fld, yrs] if v]
        suffix = " — " + " • ".join(parts) if parts else ""
        if notes:
            suffix += f" • {notes}"
        lines.append(f"- {inst}{suffix}")
    return "Education:\n" + "\n".join(lines)

def render_tutoring(p: Dict[str, Any]) -> str:
    t = p.get("tutoring_career", {})
    parts = []
    if t.get("summary"): parts.append(t["summary"])
    if t.get("since"): parts.append(f"Teaching since {t['since']}.")
    if t.get("topics"): parts.append("Topics: " + ", ".join(t["topics"]) + ".")
    if t.get("platforms"): parts.append("Platforms: " + ", ".join(t["platforms"]) + ".")
    return " ".join(parts) or "I teach coding/AI courses."

def render_professional(p: Dict[str, Any]) -> str:
    jobs: List[Dict[str, Any]] = p.get("professional_experience", [])
    if not jobs:
        return "Professional experience: (add items in PROFILE['professional_experience'])."
    out = ["Professional Experience:"]
    for j in jobs:
        title = j.get("title", "Role")
        comp  = j.get("company", "Company")
        yrs   = j.get("years", "")
        line1 = f"- {title} @ {comp}" + (f" ({yrs})" if yrs else "")
        out.append(line1)
        for h in j.get("highlights", [])[:4]:
            out.append(f"   • {h}")
    return "\n".join(out)

def render_tools(p: Dict[str, Any]) -> str:
    s = p.get("tools_and_skills", {})
    buckets = []
    for key in ["languages", "testing", "devops", "cloud", "data_ai", "other"]:
        vals = s.get(key)
        if vals:
            label = key.replace("_", " ").title()
            buckets.append(f"{label}: " + ", ".join(vals))
    return " | ".join(buckets) if buckets else "Skills: (add lists in PROFILE['tools_and_skills'])."

def render_childhood(p: Dict[str, Any]) -> str:
    return p.get("childhood", "I grew up loving technology and problem‑solving.")

def render_personal(p: Dict[str, Any]) -> str:
    info = p.get("personal_life", {})
    parts = []
    if info.get("family"):
        parts.append(info["family"])
    if info.get("hobbies"):
        parts.append("Hobbies: " + ", ".join(info["hobbies"]) + ".")
    if info.get("fun_facts"):
        parts.append("Fun facts: " + "; ".join(info["fun_facts"]) + ".")
    return " ".join(parts) or "I enjoy family time, travel, and teaching."

RENDERERS: Dict[str, Callable[[Dict[str, Any]], str]] = {
    "greeting": lambda p: "Hi! Ask about my education, tools, work, tutoring, or personal life.",
    "help": lambda p: ("You can ask about my full name, where I’m from/born, where I live, "
                       "my education, tutoring career, professional experience, tools/skills, childhood, or personal life."),
    "thanks": lambda p: "You’re welcome!",
    "full_name": render_full_name,
    "origin": render_origin,
    "current_location": render_location,
    "education": render_education,
    "tutoring_career": render_tutoring,
    "professional_career": render_professional,
    "tools_and_skills": render_tools,
    "childhood": render_childhood,
    "personal_life": render_personal,
}

def route_and_answer(user_text: str) -> str:
    X = vectorizer.transform([user_text])
    intent = model.predict(X)[0]
    key = answers_index.get(intent, "help")
    renderer = RENDERERS.get(key, RENDERERS["help"])
    return renderer(PROFILE)

# --- Enhanced Theme & CSS for better website integration ---
theme = gr.themes.Soft(
    primary_hue="indigo",
    secondary_hue="violet",
    neutral_hue="slate",
    font=gr.themes.GoogleFont("Inter"),
    font_mono=gr.themes.GoogleFont("JetBrains Mono")
)

# Get embedding mode from environment variable
EMBED_MODE = os.getenv("EMBED_MODE", "false").lower() == "true"
COMPACT_MODE = os.getenv("COMPACT_MODE", "false").lower() == "true"

custom_css = f"""
/* Enhanced CSS for better website integration */
:root {{
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.12);
  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
}}

/* Container optimizations for embedding */
.gradio-container {{
  max-width: {'100%' if EMBED_MODE else '1200px'} !important;
  margin: 0 auto !important;
  padding: {'2px' if EMBED_MODE else '8px'} !important;
  min-height: {'400px' if EMBED_MODE else 'auto'} !important;
}}

/* Remove Gradio branding for clean embedding */
.gradio-container .footer {{ display: none !important; }}
.gradio-container .gradio-footer {{ display: none !important; }}

/* Enhanced header with better visual hierarchy */
.header-card {{
  background: var(--primary-gradient);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  padding: {'8px 12px' if COMPACT_MODE else '16px 20px'};
  box-shadow: var(--shadow-soft);
  margin-bottom: 12px;
}}

/* Improved glass morphism effect */
.glass {{
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  backdrop-filter: blur(16px) !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-soft) !important;
  transition: all 0.3s ease !important;
}}

.glass:hover {{
  box-shadow: var(--shadow-hover) !important;
  transform: translateY(-2px) !important;
}}

/* Enhanced chat interface */
#chat-card {{
  padding: 16px;
  margin-bottom: 8px;
}}

#chatbox {{
  height: {f'{COMPACT_MODE and "320px" or "480px"}'} !important;
  border-radius: 12px !important;
  border: none !important;
  background: rgba(255, 255, 255, 0.02) !important;
}}

/* Modern message bubbles */
.message-wrap {{
  margin: 8px 0 !important;
}}

.message.user {{
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border-radius: 18px 18px 4px 18px !important;
  color: white !important;
  margin-left: 20% !important;
}}

.message.bot {{
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 18px 18px 18px 4px !important;
  margin-right: 20% !important;
}}

/* Enhanced sticky input with better UX */
.input-row {{
  position: sticky;
  bottom: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
  padding: 12px;
  margin-top: 8px;
  border-top: 1px solid var(--glass-border);
  border-radius: 0 0 16px 16px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}}

/* Enhanced input field */
.input-row input {{
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}}

.input-row input:focus {{
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}}

/* Modern button styling */
.input-row button {{
  border-radius: 12px !important;
  padding: 12px 20px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  border: none !important;
}}

button[variant="primary"] {{
  background: var(--primary-gradient) !important;
  color: white !important;
}}

button[variant="primary"]:hover {{
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-hover) !important;
}}

button[variant="secondary"] {{
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: #e2e8f0 !important;
}}

/* Enhanced quick action chips */
.quick-chip button {{
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 20px !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  color: #e2e8f0 !important;
}}

.quick-chip button:hover {{
  background: rgba(102, 126, 234, 0.2) !important;
  border-color: #667eea !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-soft) !important;
}}

/* Responsive design improvements */
@media (max-width: 768px) {{
  .gradio-container {{ padding: 4px !important; }}
  .main {{ order: 1; }}
  .sidebar {{ order: 2; margin-top: 16px; }}
  #chatbox {{ height: {'280px' if COMPACT_MODE else '360px'} !important; }}
  .header-card {{ padding: 12px !important; }}
  .input-row {{ padding: 8px !important; }}
  .message.user, .message.bot {{ margin-left: 5% !important; margin-right: 5% !important; }}
}}

@media (max-width: 480px) {{
  #chatbox {{ height: {'240px' if COMPACT_MODE else '300px'} !important; }}
  .quick-chip button {{ padding: 6px 12px !important; font-size: 12px !important; }}
}}

/* Loading states and animations */
.loading {{
  opacity: 0.7;
  pointer-events: none;
}}

@keyframes pulse {{
  0%, 100% {{ opacity: 1; }}
  50% {{ opacity: 0.5; }}
}}

.pulse {{ animation: pulse 2s infinite; }}

/* Custom scrollbar for chat */
#chatbox::-webkit-scrollbar {{
  width: 6px;
}}

#chatbox::-webkit-scrollbar-track {{
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}}

#chatbox::-webkit-scrollbar-thumb {{
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}}

#chatbox::-webkit-scrollbar-thumb:hover {{
  background: rgba(255, 255, 255, 0.3);
}}

/* Enhanced footer */
.footer {{
  opacity: 0.6;
  font-size: 0.8rem;
  text-align: center;
  padding: 8px 0;
  color: #94a3b8;
  display: {'none' if EMBED_MODE else 'block'};
}}
"""

# ---- Enhanced UI with better embedding support ----
def create_header_html():
    """Create responsive header HTML"""
    if COMPACT_MODE:
        return """
        <div style="display:flex;align-items:center;gap:10px;">
          <div style="width:32px;height:32px;border-radius:8px;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center;font-size:16px;">🤖</div>
          <div style="display:flex;flex-direction:column;">
            <div style="font-weight:700;font-size:0.95rem;color:white;">Faruk Hasan</div>
            <div style="color:#cbd5e1;font-size:0.8rem;">Personal Assistant</div>
          </div>
        </div>
        """
    else:
        return """
        <div style="display:flex;align-items:center;gap:16px;">
          <div style="width:48px;height:48px;border-radius:12px;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center;font-size:24px;box-shadow:0 4px 12px rgba(102,126,234,0.3);">🤖</div>
          <div style="display:flex;flex-direction:column;">
            <div style="font-weight:700;font-size:1.2rem;letter-spacing:0.3px;color:white;text-shadow:0 2px 4px rgba(0,0,0,0.3);">Faruk Hasan — Personal Chatbot</div>
            <div style="color:#cbd5e1;font-size:0.95rem;margin-top:2px;">Ask about education, tools, work, tutoring, or personal life</div>
          </div>
        </div>
        """

with gr.Blocks(
    title="Faruk Hasan – Personal Chatbot",
    theme=theme,
    css=custom_css,
    head="""
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Personal chatbot for Faruk Hasan - Ask about education, career, skills, and more">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    """
) as demo:

    # Conditional header - hide in embed mode
    if not EMBED_MODE:
        with gr.Row(elem_classes=["header-card"]):
            gr.HTML(create_header_html())

    # Main layout with responsive design
    with gr.Row():
        # MAIN CHAT FIRST (mobile-first approach)
        with gr.Column(scale=8 if not COMPACT_MODE else 12, min_width=320, elem_classes=["main"]):
            with gr.Group(elem_id="chat-card", elem_classes=["glass"]):
                # Enhanced chatbot with better configuration
                chat = gr.Chatbot(
                    label=None,
                    height=480 if not COMPACT_MODE else 320,
                    elem_id="chatbox",
                    show_copy_button=True,
                    show_share_button=False,
                    avatar_images=("👤", "🤖"),
                    bubble_full_width=False,
                    layout="panel" if not COMPACT_MODE else "bubble",
                )

                # Enhanced input section with better UX
                with gr.Row(elem_classes=["input-row"]):
                    with gr.Column(scale=8):
                        msg = gr.Textbox(
                            placeholder="Ask me something… (e.g., 'education', 'tools', 'where are you from')",
                            show_label=False,
                            autofocus=True,
                            max_lines=3,
                            container=False,
                        )
                    with gr.Column(scale=1, min_width=80):
                        send = gr.Button("Send", variant="primary", size="sm")
                    with gr.Column(scale=1, min_width=80):
                        clear = gr.Button("Clear", variant="secondary", size="sm")

        # ENHANCED SIDEBAR (hidden in compact/embed mode)
        if not COMPACT_MODE and not EMBED_MODE:
            with gr.Column(scale=4, min_width=280, elem_classes=["sidebar"]):
                # Quick Questions Section
                with gr.Group(elem_classes=["glass"]):
                    gr.Markdown("#### 🔎 Quick Questions")
                    gr.Markdown("*Click any topic to get instant answers*")

                    # Organized chips by category
                    with gr.Row():
                        chips = []
                        # Personal Info
                        chips.append(gr.Button("👤 Full name", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("🌍 Where are you from?", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("📍 Where do you live?", size="sm", elem_classes=["quick-chip"]))

                    with gr.Row():
                        # Professional
                        chips.append(gr.Button("🎓 Education", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("👨‍🏫 Tutoring career", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("💼 Professional experience", size="sm", elem_classes=["quick-chip"]))

                    with gr.Row():
                        # Skills & Personal
                        chips.append(gr.Button("🛠️ Tools & skills", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("👶 Childhood", size="sm", elem_classes=["quick-chip"]))
                        chips.append(gr.Button("🏠 Personal life", size="sm", elem_classes=["quick-chip"]))
                # Tips and Help Section
                with gr.Group(elem_classes=["glass"]):
                    gr.Markdown("#### 💡 Usage Tips")
                    gr.Markdown(
                    "- Short prompts like **“full name”**, **“degree”**, **“kids”** work great.\n"
                    "- Answers come from a structured profile (always consistent).\n"
                    "- **Clear** resets the chat."
                        """
                        **Quick Tips:**
                        - Use short prompts like *"education"*, *"skills"*, *"family"*
                        - All answers come from a structured profile
                        - Responses are always consistent and up-to-date
                        - Click **Clear** to reset the conversation

                        **Try asking:**
                        - "What programming languages do you know?"
                        - "Tell me about your work experience"
                        - "Do you have kids?"
                        """
                    )
        else:
            # Compact mode - show essential chips below chat
            with gr.Row():
                gr.Markdown("**Quick topics:**")
            with gr.Row():
                chips = [
                    gr.Button("About", size="sm", elem_classes=["quick-chip"]),
                    gr.Button("Education", size="sm", elem_classes=["quick-chip"]),
                    gr.Button("Work", size="sm", elem_classes=["quick-chip"]),
                    gr.Button("Skills", size="sm", elem_classes=["quick-chip"]),
                    gr.Button("Personal", size="sm", elem_classes=["quick-chip"]),
                ]

    # Enhanced footer with conditional display
    if not EMBED_MODE:
        gr.HTML('<div class="footer">© 2025 Faruk Hasan — Personal Chatbot</div>')

    # --- Logic bindings ---
    def respond(message, history):
        reply = route_and_answer(message)
        history = history or []
        history.append((message, reply))
        return "", history

    def inject_and_send(prompt, history):
        reply = route_and_answer(prompt)
        history = history or []
        history.append((prompt, reply))
        return history

    msg.submit(respond, [msg, chat], [msg, chat])
    send.click(respond, [msg, chat], [msg, chat])
    clear.click(lambda: ([],), outputs=[chat])

    chips[0].click(lambda h: inject_and_send("full name", h), inputs=[chat], outputs=[chat])
    chips[1].click(lambda h: inject_and_send("where are you from", h), inputs=[chat], outputs=[chat])
    chips[2].click(lambda h: inject_and_send("where do you live", h), inputs=[chat], outputs=[chat])
    chips[3].click(lambda h: inject_and_send("education", h), inputs=[chat], outputs=[chat])
    chips[4].click(lambda h: inject_and_send("tutoring career", h), inputs=[chat], outputs=[chat])
    chips[5].click(lambda h: inject_and_send("professional career", h), inputs=[chat], outputs=[chat])
    chips[6].click(lambda h: inject_and_send("tools and skills", h), inputs=[chat], outputs=[chat])
    chips[7].click(lambda h: inject_and_send("childhood", h), inputs=[chat], outputs=[chat])
    chips[8].click(lambda h: inject_and_send("personal life", h), inputs=[chat], outputs=[chat])

if __name__ == "__main__":
    demo.launch(server_name="0.0.0.0", server_port=int(os.getenv("PORT", "7860")))
